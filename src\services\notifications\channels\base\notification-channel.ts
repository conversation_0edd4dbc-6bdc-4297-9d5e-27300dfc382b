/**
 * Interface base para canais de notificação
 * Define o contrato que todos os canais devem implementar
 */

import type { NotificationChannel, NotificationType } from '../../types/notification-types';

export interface NotificationChannelData {
  tenantId: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  templateId?: string;
  variables?: Record<string, any>;
}

export interface NotificationChannelResult {
  success: boolean;
  messageId?: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface BatchNotificationResult {
  success: boolean;
  results: NotificationChannelResult[];
  totalSent: number;
  totalFailed: number;
  errors: string[];
}

export interface DeliveryStatus {
  messageId: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'bounced' | 'complained';
  timestamp: string;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface base que todos os canais de notificação devem implementar
 */
export abstract class NotificationChannelBase {
  protected channelType: NotificationChannel;

  constructor(channelType: NotificationChannel) {
    this.channelType = channelType;
  }

  /**
   * Envia uma notificação através do canal
   */
  abstract send(data: NotificationChannelData): Promise<NotificationChannelResult>;

  /**
   * Envia múltiplas notificações em lote
   */
  abstract sendBatch(notifications: NotificationChannelData[]): Promise<BatchNotificationResult>;

  /**
   * Verifica o status de entrega de uma notificação
   */
  abstract getDeliveryStatus(messageId: string): Promise<DeliveryStatus>;

  /**
   * Valida se o canal está configurado corretamente
   */
  abstract validateConfiguration(): Promise<boolean>;

  /**
   * Retorna o tipo do canal
   */
  getChannelType(): NotificationChannel {
    return this.channelType;
  }
}
