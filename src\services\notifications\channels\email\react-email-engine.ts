/**
 * Engine de renderização de templates React Email
 * Suporta templates personalizáveis com variáveis dinâmicas
 */

import { render } from '@react-email/render';
import { createClient } from '@/services/supabase/server';
import type { NotificationTemplate, TemplateVariable } from '../../types/notification-types';

// Importar templates React Email
import { PaymentReminderTemplate } from './templates/payment-reminder-template';
import { ClassReminderTemplate } from './templates/class-reminder-template';

export interface RenderedTemplate {
  subject: string;
  body: string;
}

export interface RenderedEmail {
  subject: string;
  html: string;
  text: string;
}

export class ReactEmailEngine {
  /**
   * Renderiza um template de notificação in-app
   */
  async renderTemplate(templateId: string, variables: Record<string, any>): Promise<RenderedTemplate> {
    try {
      const supabase = await createClient();
      
      // Buscar template
      const { data: template, error } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('id', templateId)
        .eq('is_active', true)
        .single();

      if (error || !template) {
        throw new Error(`Template não encontrado: ${templateId}`);
      }

      // Renderizar subject e body
      const subject = this.replaceVariables(template.subject_template || template.name, variables);
      const body = this.replaceVariables(template.body_template, variables);

      return {
        subject,
        body
      };

    } catch (error) {
      console.error('Erro ao renderizar template:', error);
      throw error;
    }
  }

  /**
   * Renderiza um template de e-mail usando React Email
   */
  async renderEmail(templateId: string, variables: Record<string, any>): Promise<RenderedEmail> {
    try {
      // Determinar qual template React usar baseado no templateId
      const templateType = this.getTemplateType(templateId);
      
      let reactElement;
      let subject = '';

      switch (templateType) {
        case 'payment_reminder':
          subject = `Lembrete de Pagamento - ${variables.planName || 'Mensalidade'}`;
          reactElement = PaymentReminderTemplate({
            academyName: variables.academyName || 'Academia',
            academyLogo: variables.academyLogo,
            primaryColor: variables.primaryColor,
            secondaryColor: variables.secondaryColor,
            studentName: variables.studentName || 'Aluno',
            amount: variables.amount || 0,
            dueDate: variables.dueDate || new Date().toISOString(),
            planName: variables.planName || 'Plano',
            paymentMethod: variables.paymentMethod,
            invoiceUrl: variables.invoiceUrl,
            currency: variables.currency || 'BRL',
            lateFee: variables.lateFee,
            gracePeriod: variables.gracePeriod
          });
          break;

        case 'class_reminder':
          subject = `Lembrete de Aula - ${variables.className || 'Treino'}`;
          reactElement = ClassReminderTemplate({
            academyName: variables.academyName || 'Academia',
            academyLogo: variables.academyLogo,
            primaryColor: variables.primaryColor,
            secondaryColor: variables.secondaryColor,
            studentName: variables.studentName || 'Aluno',
            className: variables.className || 'Aula',
            instructorName: variables.instructorName || 'Instrutor',
            classDate: variables.classDate || new Date().toISOString(),
            classTime: variables.classTime || '19:00',
            location: variables.location,
            duration: variables.duration,
            maxStudents: variables.maxStudents,
            currentEnrollments: variables.currentEnrollments,
            classDetailsUrl: variables.classDetailsUrl,
            cancelUrl: variables.cancelUrl,
            rescheduleUrl: variables.rescheduleUrl,
            reminderType: variables.reminderType || 'upcoming',
            requiresEquipment: variables.requiresEquipment,
            specialInstructions: variables.specialInstructions
          });
          break;

        default:
          // Fallback para template simples
          return await this.renderSimpleEmail(templateId, variables);
      }

      // Renderizar o componente React para HTML
      const html = await render(reactElement);
      const text = this.stripHtml(html);

      return {
        subject,
        html,
        text
      };

    } catch (error) {
      console.error('Erro ao renderizar e-mail:', error);
      // Fallback para template simples em caso de erro
      return await this.renderSimpleEmail(templateId, variables);
    }
  }

  /**
   * Renderiza um template de WhatsApp
   */
  async renderWhatsApp(templateId: string, variables: Record<string, any>): Promise<string> {
    try {
      const rendered = await this.renderTemplate(templateId, variables);
      return this.stripHtml(rendered.body);

    } catch (error) {
      console.error('Erro ao renderizar WhatsApp:', error);
      throw error;
    }
  }

  /**
   * Determina o tipo de template baseado no ID
   */
  private getTemplateType(templateId: string): string {
    // Se o templateId contém o tipo, extrair
    if (templateId.includes('payment')) return 'payment_reminder';
    if (templateId.includes('class')) return 'class_reminder';
    if (templateId.includes('enrollment')) return 'enrollment_welcome';
    if (templateId.includes('system')) return 'system_notification';
    if (templateId.includes('event')) return 'event_notification';
    
    // Fallback: tentar extrair do padrão tipo_default
    const parts = templateId.split('_');
    if (parts.length > 0) {
      return `${parts[0]}_reminder`;
    }
    
    return 'simple';
  }

  /**
   * Renderiza um template simples (fallback)
   */
  private async renderSimpleEmail(templateId: string, variables: Record<string, any>): Promise<RenderedEmail> {
    try {
      const rendered = await this.renderTemplate(templateId, variables);
      
      return {
        subject: rendered.subject,
        html: `
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>${rendered.subject}</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <h1 style="color: ${variables.primaryColor || '#007291'}; margin-bottom: 20px;">
                  ${variables.academyName || 'Academia'}
                </h1>
                <div style="background: white; padding: 20px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                  ${rendered.body}
                </div>
                <p style="text-align: center; color: #666; font-size: 12px; margin-top: 20px;">
                  ${variables.academyName || 'Academia'} - Este e-mail foi enviado automaticamente.
                </p>
              </div>
            </body>
          </html>
        `,
        text: this.stripHtml(rendered.body)
      };

    } catch (error) {
      console.error('Erro ao renderizar template simples:', error);
      throw error;
    }
  }

  /**
   * Substitui variáveis no template
   */
  private replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template;

    // Substituir variáveis no formato {{variableName}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value || ''));
    });

    return result;
  }

  /**
   * Remove tags HTML de uma string
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').trim();
  }

  /**
   * Valida se todas as variáveis obrigatórias estão presentes
   */
  async validateVariables(templateId: string, variables: Record<string, any>): Promise<{
    isValid: boolean;
    missingVariables: string[];
  }> {
    try {
      const supabase = await createClient();
      
      // Buscar template
      const { data: template } = await supabase
        .from('notification_templates')
        .select('type')
        .eq('id', templateId)
        .single();

      if (!template) {
        return { isValid: false, missingVariables: [] };
      }

      // Buscar variáveis obrigatórias para este tipo de template
      const { data: requiredVariables } = await supabase
        .from('template_variables')
        .select('variable_key')
        .eq('template_type', template.type)
        .eq('is_required', true);

      const missingVariables: string[] = [];
      
      requiredVariables?.forEach(variable => {
        if (!(variable.variable_key in variables) || variables[variable.variable_key] === null || variables[variable.variable_key] === undefined) {
          missingVariables.push(variable.variable_key);
        }
      });

      return {
        isValid: missingVariables.length === 0,
        missingVariables
      };

    } catch (error) {
      console.error('Erro ao validar variáveis:', error);
      return { isValid: false, missingVariables: [] };
    }
  }
}
