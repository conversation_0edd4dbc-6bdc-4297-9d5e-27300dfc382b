{"private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "npm run build", "build:profile": "npm run build -- --profile", "build:debug": "npm run build -- --debug", "start": "next start", "setup-subdomains": "node scripts/setup-local-subdomains.js", "generate-phone-metadata": "libphonenumber-metadata-generator src/lib/metadata.custom.json --countries BR"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@lottiefiles/dotlottie-react": "^0.14.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.0", "@react-email/components": "^0.3.2", "@react-email/render": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.2", "@types/js-cookie": "^3.0.6", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "framer-motion": "^12.16.0", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.12.9", "lucide-react": "^0.468.0", "motion": "^12.5.0", "next": "^15.2.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "prettier": "^3.3.3", "qrcode": "^1.5.4", "react": "19.0.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-qrcode-pix": "^5.0.0", "recharts": "^3.0.2", "resend": "^4.7.0", "sonner": "^2.0.3", "uuid": "^11.1.0", "zod": "^3.25.42", "zustand": "^5.0.6"}, "overrides": {"react-is": "^19.0.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.1", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/papaparse": "^5.3.16", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "cross-env": "^7.0.3", "libphonenumber-metadata-generator": "^1.1.0", "postcss": "8.4.49", "supabase": "^2.30.4", "tailwind-merge": "^2.6.0", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2"}}